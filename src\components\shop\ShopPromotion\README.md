# ShopPromotion Component - <PERSON><PERSON>i tiến chức năng thêm sản phẩm khuyến mãi

## Tổng quan thay đổi

Đã sửa lại hoàn toàn chức năng "Thêm sản phẩm khuyến mãi" với cách hoạt động mới sử dụng PageView.

## Cách hoạt động mới

### Page 1: Chọn danh mục cha
- **Dữ liệu**: L<PERSON>y tất cả danh mục cha từ bảng Category với `ParentId` rỗng
- **API**: `categoryDA.filterByEmptyKey({ key: 'ParentId', notEmpty: false })`
- **Tính năng**:
  - Tìm kiếm danh mục theo tên
  - Chọn 1 danh mục cha
  - Nút mũi tên để chuyển sang page tiếp theo
  - Nú<PERSON> "Đóng" để đóng bottom sheet
  - <PERSON><PERSON><PERSON> "Tiếp theo" (disabled khi chưa chọn danh mục)

### Page 2: <PERSON><PERSON><PERSON> danh mục con
- **Dữ liệu**: <PERSON><PERSON><PERSON> danh mụ<PERSON> con của danh mục được chọn từ page 1
- **API**: `categoryDA.getListSimple({ query: '@ParentId:{parentId}' })`
- **Tính năng**:
  - Hiển thị tên danh mục cha trong header
  - Chọn 1 danh mục con
  - Nút "Back" để quay về page 1
  - Nút "Tiếp theo" (disabled khi chưa chọn danh mục con)

### Page 3: Chọn sản phẩm
- **Dữ liệu**: Lấy sản phẩm theo danh mục con được chọn, chỉ sản phẩm chưa có khuyến mãi
- **API**: `productDA.getListSimple({ query: '@ShopId:{shopId} @CategoryId:{categoryId} -@Discount' })`
- **Tính năng**:
  - Hiển thị danh sách sản phẩm với ảnh, tên, giá
  - Chọn nhiều sản phẩm (multi-select)
  - Nút "Back" để quay về page 2
  - Nút "Xác nhận" (disabled khi chưa chọn sản phẩm nào)

### Modal nhập khuyến mãi
- **Kích hoạt**: Sau khi chọn sản phẩm và ấn "Xác nhận" ở page 3
- **Tính năng**:
  - Nhập % khuyến mãi (0-100)
  - Validation input
  - Cập nhật tất cả sản phẩm đã chọn với % khuyến mãi
  - Đóng bottom sheet sau khi thành công

## Cải tiến so với phiên bản cũ

### 1. UX/UI tốt hơn
- ✅ Luồng rõ ràng, từng bước một
- ✅ Navigation dễ hiểu với back/next buttons
- ✅ Visual feedback cho selection
- ✅ Search chỉ ở page đầu tiên (đơn giản hóa)

### 2. Performance tốt hơn
- ✅ Không gọi API nhiều lần trong vòng lặp
- ✅ Load data theo từng step
- ✅ Tối ưu re-render

### 3. Code structure tốt hơn
- ✅ Logic rõ ràng, dễ maintain
- ✅ Ít state phức tạp
- ✅ Error handling tốt hơn

### 4. Tính năng mới
- ✅ Multi-select products
- ✅ Input validation
- ✅ Better error messages
- ✅ Success feedback

## API sử dụng

```typescript
// Load parent categories
categoryDA.filterByEmptyKey({
  key: 'ParentId',
  notEmpty: false,
  sortby: [{ prop: 'Name', direction: 'ASC' }],
});

// Load child categories
categoryDA.getListSimple({
  query: `@ParentId:{${parentId}}`,
  sortby: { BY: 'Name', DIRECTION: 'ASC' },
});

// Load products without discount
productDA.getListSimple({
  query: `@ShopId:{${shopId}} @CategoryId:{${categoryId}} -@Discount`,
  sortby: { BY: 'Name', DIRECTION: 'ASC' },
});

// Update products with discount
productDA.edit(updatedProducts);
```

## States quản lý

```typescript
const [currentPage, setCurrentPage] = useState<number>(0); // 0, 1, 2
const [parentCategories, setParentCategories] = useState<any[]>([]);
const [childCategories, setChildCategories] = useState<any[]>([]);
const [products, setProducts] = useState<any[]>([]);
const [selectedCategory, setSelectedCategory] = useState<any>(null);
const [selectedChildCategory, setSelectedChildCategory] = useState<any>(null);
const [selectedProducts, setSelectedProducts] = useState<any[]>([]);
const [searchText, setSearchText] = useState<string>('');
const [filteredData, setFilteredData] = useState<any[]>([]);
```

## Cách sử dụng

Component hoạt động tự động khi được mount trong BottomSheet. User chỉ cần:

1. Chọn danh mục cha → Ấn "Tiếp theo" hoặc mũi tên
2. Chọn danh mục con → Ấn "Tiếp theo" hoặc mũi tên  
3. Chọn sản phẩm → Ấn "Xác nhận"
4. Nhập % khuyến mãi → Ấn "Đồng ý"

Component sẽ tự động cập nhật danh sách sản phẩm khuyến mãi và đóng bottom sheet.
