/* eslint-disable react-native/no-inline-styles */
import React, { forwardRef, useEffect, useRef, useState } from 'react';
import { useTranslation } from 'react-i18next';
import {
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
  ScrollView,
  Image,
  Pressable,
  Dimensions,
  KeyboardAvoidingView,
  Platform,
  FlatList,
  Modal,
} from 'react-native';

import { TypoSkin } from '../../../assets/skin/typography';
import {
  AppSvg,
  Checkbox,
  ComponentStatus,
  FBottomSheet,
  hideBottomSheet,
  showBottomSheet,
  showSnackbar,
  showDialog,
  FDialog,
} from 'wini-mobile-components';
import iconSvg from '../../../svg/icon';
import { ColorThemes } from '../../../assets/skin/colors';
import ModalPromotion from './ModalPromotion';
import WScreenFooter from '../../../Screen/Layout/footer';
import { AppDispatch, RootState } from '../../../redux/store/store';
import { useDispatch, useSelector } from 'react-redux';
import ScreenHeader from '../../../Screen/Layout/header';
import { TextInput } from 'react-native-paper';
import { fetchCategories } from '../../../redux/actions/categoryAction';
import { useSelectorShopState } from '../../../redux/hook/shopHook ';
import ConfigAPI from '../../../Config/ConfigAPI';
import { DataController } from '../../../base/baseController';
import EmptyPage from '../../../Screen/emptyPage';

const ShopPromortionComponent = () => {
  const popupRef = useRef<any>(null);
  const dialogRef = useRef<any>(null);
  const [activeButton, setActiveButton] = useState<string>('Tất cả');
  const [productPromotion, setProductPromotion] = useState<any[]>([]);
  const [isShow, setIsShow] = useState<boolean>(false);
  const [getSelectProduct, setGetSelectProduct] = useState<string>('');
  const [discountValue, setDiscountValue] = useState<string>('');
  const [dataDiscount, setDataDiscount] = useState<any[]>([]);
  const [allCategoryIds, setAllCategoryIds] = useState<any[]>([]);
  const [selectedProducts, setSelectedProducts] = useState<string[]>([]);
  const [selectAll, setSelectAll] = useState<boolean>(false);
  const [showDeleteModal, setShowDeleteModal] = useState<boolean>(false);
  const dispatch = useDispatch<any>();
  const productDA = new DataController('Product');
  const shopInfo = useSelectorShopState().data;
  const { data: dataCategory } = useSelector(
    (state: RootState) => state.category,
  );
  const handleShow = () => {
    setIsShow(true);
  };

  const closeModal = () => {
    setIsShow(false);
  };



  const getAllCategoryIds = (dataCategory: any) => {
    let ids: any[] = [];
    dataCategory.forEach((cat: any) => {
      // Luôn thêm ID của category cha
      ids.push({
        Id: cat.Id,
        Name: cat.Name,
      });

      if (cat.Children && cat.Children.length > 0) {
        // Nếu có Children, lấy Id của các Children
        ids = ids.concat(
          cat.Children.map((child: any) => {
            return {
              Id: child.Id,
              Name: child.Name,
            };
          }),
        );
      }
    });
    setAllCategoryIds(ids);
  };

  const handleEditPromotion = (item: any) => {
    setGetSelectProduct(item?.Id);
  };

  const handleCancelEditPromotion = () => {
    setGetSelectProduct('');
  };

  const getInforProductPromotion = async (CategoryId?: string) => {
    console.log('=== getInforProductPromotion START ===');
    console.log('check-CategoryId', CategoryId);
    console.log('check-shopInfo', shopInfo);
    debugger
    if (!shopInfo || !shopInfo[0] || !shopInfo[0].Id) {
      console.error('Missing shopInfo or shopId, skipping getInforProductPromotion');
      return;
    }

    try {
      let response;
      const shopId = shopInfo[0].Id;
      console.log('Using shopId:', shopId, 'Type:', typeof shopId);

      if (CategoryId) {
        const query = `@ShopId:${shopId} @CategoryId:${CategoryId}`;
        console.log('Query with category:', query);
        console.log('About to call productDA.getListSimple with category...');
        response = await productDA.getListSimple({
          query: query,
          sortby: { BY: 'DateCreated', DIRECTION: 'DESC' },
        });
      } else {
        const query = `@ShopId:${shopId}`;
        console.log('Query without category:', query);
        console.log('About to call productDA.getListSimple without category...');
        response = await productDA.getListSimple({
          query: query,
          sortby: { BY: 'DateCreated', DIRECTION: 'DESC' },
        });
      }

      console.log('=== getInforProductPromotion RESPONSE ===');
      console.log('getInforProductPromotion response:', response);
      if (response?.code === 200) {
        if (response?.data && response?.data?.length > 0) {
          // Filter products that have discount
          const productsWithDiscount = response.data.filter((item: any) => item.Discount && item.Discount > 0);
          console.log('Products with discount:', productsWithDiscount);
          setProductPromotion(productsWithDiscount);
        } else {
          setProductPromotion([]);
        }
        console.log('=== getInforProductPromotion SUCCESS ===');
      }
    } catch (error: any) {
      console.error('=== getInforProductPromotion ERROR ===');
      console.error('Error in getInforProductPromotion:', error);
      console.error('Error message:', error?.message);
      console.error('Error stack:', error?.stack);
      setProductPromotion([]);
    }
  };

  const editPromotion = async (item: any) => {
    let respone = await productDA.edit([
      { ...item, Discount: Number(discountValue) },
    ]);
    console.log('check-respone', respone);
    if (respone?.code === 200) {
      getInforProductPromotion();
      setGetSelectProduct('');
    }
  };

  const deletePromotion = async (item: any) => {
    showDialog({
      ref: dialogRef,
      status: ComponentStatus.WARNING,
      title: 'Bạn chắc chắn xoá khuyến mãi này?',
      onSubmit: async () => {
        try {
          let response = await productDA.edit([{ ...item, Discount: 0 }]);
          if (response?.code === 200) {
            // Cập nhật danh sách sản phẩm
            await getInforProductPromotion();
            setGetSelectProduct('');

            // Thông báo thành công
            showSnackbar({
              message: 'Xóa khuyến mãi thành công',
              status: ComponentStatus.SUCCSESS,
            });
          } else {
            // Xử lý lỗi từ API
            showSnackbar({
              message: response?.message || 'Có lỗi xảy ra khi xóa khuyến mãi',
              status: ComponentStatus.ERROR,
            });
          }
        } catch (error) {
          console.error('Error deleting promotion:', error);
          // Xử lý lỗi network hoặc lỗi khác
          showSnackbar({
            message: 'Không thể kết nối đến máy chủ. Vui lòng thử lại sau.',
            status: ComponentStatus.ERROR,
          });
        }
      },
    });

  };

  useEffect(() => {
    console.log('=== MAIN useEffect [shopInfo] START - DISABLED FOR DEBUGGING ===');
    // TEMPORARILY DISABLED TO FIND THE ERROR
    // try {
    //   dispatch(fetchCategories());
    //   getAllCategoryIds(dataCategory);
    //   if (shopInfo && shopInfo.length > 0) {
    //     console.log('Calling getInforProductPromotion from main useEffect...');
    //     getInforProductPromotion();
    //   } else {
    //     console.error('No shop info available for getInforProductPromotion');
    //   }
    // } catch (error: any) {
    //   console.error('=== MAIN useEffect ERROR ===');
    //   console.error('Error in main useEffect:', error);
    // }
  }, [shopInfo]);

  useEffect(() => {
    console.log('=== useEffect [isShow] START - DISABLED FOR DEBUGGING ===');
    // TEMPORARILY DISABLED TO FIND THE ERROR
    // try {
    //   if (shopInfo && shopInfo.length > 0) {
    //     console.log('Calling getInforProductPromotion from isShow useEffect...');
    //     getInforProductPromotion();
    //   } else {
    //     console.log('Skipping getInforProductPromotion in isShow useEffect - no shopInfo');
    //   }
    // } catch (error: any) {
    //   console.error('=== useEffect [isShow] ERROR ===');
    //   console.error('Error in isShow useEffect:', error);
    // }
  }, [isShow]);

  // Cập nhật trạng thái selectAll khi selectedProducts thay đổi
  useEffect(() => {
    if (productPromotion.length > 0) {
      const allSelected = productPromotion.every(product =>
        selectedProducts.includes(product.Id),
      );
      setSelectAll(allSelected);
    }
  }, [selectedProducts, productPromotion]);

  const handleSelectAllData = (ref: any) => {
    hideBottomSheet(ref as any);
  };
  const handleSelectMenu = async (type: string, item?: any) => {
    console.log('check-item', item);
    setActiveButton(type);

    if (!shopInfo || !shopInfo[0] || !shopInfo[0].Id) {
      console.error('Missing shopInfo in handleSelectMenu');
      return;
    }

    if (!item) {
      await getInforProductPromotion();
    } else {
      await getInforProductPromotion(item?.Id);
    }
  };

  // Hàm xử lý chọn/bỏ chọn một sản phẩm
  const handleSelectProduct = (productId: string) => {
    setSelectedProducts(prev => {
      if (prev.includes(productId)) {
        return prev.filter(id => id !== productId);
      } else {
        return [...prev, productId];
      }
    });
  };

  // Hàm xử lý chọn tất cả sản phẩm
  const handleSelectAllProducts = () => {
    if (selectAll) {
      setSelectedProducts([]);
      setSelectAll(false);
    } else {
      const allProductIds = productPromotion.map(product => product.Id);
      setSelectedProducts(allProductIds);
      setSelectAll(true);
    }
  };

  // Hàm lấy danh sách sản phẩm đã chọn
  const getSelectedProductsData = () => {
    return productPromotion.filter(product =>
      selectedProducts.includes(product.Id),
    );
  };

  // Hàm xử lý xóa các sản phẩm đã chọn
  const handleDeleteSelectedProducts = async () => {
    console.log('check-selectedProducts', selectedProducts);
    if (selectedProducts.length === 0) {
      showSnackbar({ message: 'Vui lòng chọn sản phẩm cần xóa' });
      return;
    }
    // Hiển thị modal xác nhận thay vì xóa ngay
    setShowDeleteModal(true);
  };

  // Hàm thực hiện xóa sau khi xác nhận
  const confirmDeleteSelectedProducts = async () => {
    const selectedProductsData = getSelectedProductsData();
    let arrayData: any[] = [];
    for (let product of selectedProductsData) {
      arrayData.push({ ...product, Discount: 0 });
    }

    let response = await productDA.edit(arrayData);
    if (response?.code === 200) {
      if (shopInfo && shopInfo[0] && shopInfo[0].Id) {
        getInforProductPromotion();
      }
      setSelectedProducts([]);
      setSelectAll(false);
      setShowDeleteModal(false);
      showSnackbar({ message: 'Xóa khuyến mãi thành công' });
    }
  };

  // Hàm xử lý cập nhật khuyến mãi cho các sản phẩm đã chọn
  const handleUpdateSelectedProducts = () => {
    if (selectedProducts.length === 0) {
      showSnackbar({ message: 'Vui lòng chọn sản phẩm cần cập nhật' });
      return;
    }

    const selectedProductsData = getSelectedProductsData();
    setDataDiscount(selectedProductsData);
    handleShow();
  };

  return (
    <View style={styles.content}>
      <FDialog ref={dialogRef} />
      <FBottomSheet ref={popupRef} />
      <View style={styles.PromotionMenu}>
        <Text style={styles.label}>Danh sách</Text>
        <TouchableOpacity
          style={styles.deleteButton}
          onPress={() => handleDeleteSelectedProducts()}>
          <AppSvg SvgSrc={iconSvg.delete} size={12} />
          <Text style={styles.buttonText}>Xóa hàng loạt</Text>
        </TouchableOpacity>
        <TouchableOpacity
          style={styles.updateButton}
          onPress={() => handleUpdateSelectedProducts()}>
          <AppSvg SvgSrc={iconSvg.updateAll} size={12} />
          <Text style={styles.buttonTextSuccess}>Cập nhật hàng loạt</Text>
        </TouchableOpacity>
      </View>
      <View style={{ flexDirection: 'row', alignItems: 'center' }}>
        <TouchableOpacity
          style={
            activeButton === 'Tất cả' ? styles.activeButton : styles.button
          }
          onPress={() => handleSelectMenu('Tất cả')}>
          <Text style={styles.buttonTextMenu}>
            Tất cả ({productPromotion?.length})
          </Text>
        </TouchableOpacity>
        <FlatList
          data={allCategoryIds}
          style={{ flex: 1 }}
          keyExtractor={(item, i) => `${i} ${item.Id}`}
          horizontal={true}
          renderItem={({ item, index }) => (
            <View style={styles.PromorionNabar} key={`item-${index}`}>
              <TouchableOpacity
                style={
                  activeButton === item?.Name
                    ? styles.activeButton
                    : styles.button
                }
                onPress={() => handleSelectMenu(item?.Name, item)}>
                <Text style={styles.buttonTextMenu}>{item?.Name}</Text>
              </TouchableOpacity>
            </View>
          )}
        />
      </View>
      <Pressable style={styles.menuDetail}>
        <View style={styles.header}>
          <Text style={styles.headerText}>
            <Checkbox
              value={selectAll}
              onChange={() => handleSelectAllProducts()}
              size={24}
            />
          </Text>
          <Text style={styles.headerText}>Ảnh</Text>
          <Text style={styles.headerText}>
            Sản phẩm <AppSvg SvgSrc={iconSvg.dropDown} size={16} />
          </Text>
          <Text style={styles.headerText}>
            KM <AppSvg SvgSrc={iconSvg.dropDown} size={16} />
          </Text>
          <Text style={styles.headerText}>
            TT <AppSvg SvgSrc={iconSvg.dropDown} size={16} />
          </Text>
        </View>
        {productPromotion && productPromotion?.length > 0 ? (
          <FlatList
            data={productPromotion}
            style={{ flex: 1, height: '100%' }}
            ListFooterComponent={() => <View style={{ height: 100 }} />}
            keyExtractor={(item, i) => `${i} ${item.Id}`}
            renderItem={({ item, index }) => (
              <Pressable style={styles.row} key={`item-${index}`}>
                <View style={{}}>
                  <Checkbox
                    value={selectedProducts.includes(item.Id)}
                    onChange={() => handleSelectProduct(item.Id)}
                    size={24}
                  />
                </View>
                <View style={styles.imageContainer}>
                  <Image
                    source={{
                      uri: ConfigAPI.urlImg + item?.Img,
                    }} // Thay bằng URL ảnh thực tế
                    style={styles.image}
                  />
                </View>
                <Text style={styles.text}>{item?.Name}</Text>
                {getSelectProduct !== item?.Id ? (
                  <Text style={styles.textTwo}>
                    {item?.Discount ? item?.Discount : 0}%
                  </Text>
                ) : (
                  <TextInput
                    style={{
                      width: 60,
                      height: 35,
                      marginRight: 20,
                      backgroundColor: '#f8f9fa',
                      color: 'black',
                      borderWidth: 1,
                      borderColor: '#e0e0e0',
                      paddingHorizontal: 8,
                      paddingVertical: 6,
                      textAlign: 'center',
                      fontSize: 14,
                      fontWeight: '500',
                    }}
                    value={discountValue}
                    defaultValue={item?.Discount}
                    onChange={e => setDiscountValue(e.nativeEvent.text)}
                    keyboardType="numeric"
                    placeholder="0"
                    placeholderTextColor="#999"
                  />
                )}

                {getSelectProduct !== item?.Id ? (
                  <View style={styles.actions}>
                    <TouchableOpacity onPress={() => handleEditPromotion(item)}>
                      <AppSvg SvgSrc={iconSvg.editPromotion} size={24} />
                    </TouchableOpacity>
                    <TouchableOpacity onPress={() => deletePromotion(item)}>
                      <AppSvg SvgSrc={iconSvg.deletePromotion} size={24} />
                    </TouchableOpacity>
                  </View>
                ) : (
                  <View style={styles.actions}>
                    <TouchableOpacity onPress={() => editPromotion(item)}>
                      <AppSvg SvgSrc={iconSvg.confirm} size={24} />
                    </TouchableOpacity>
                    <TouchableOpacity
                      onPress={() => handleCancelEditPromotion()}>
                      <AppSvg SvgSrc={iconSvg.cancelEdit} size={24} />
                    </TouchableOpacity>
                  </View>
                )}
              </Pressable>
            )}
          />
        ) : (
          <View
            style={{
              flex: 1,
              alignItems: 'center',
              justifyContent: 'center',
              paddingBottom: 100,
            }}>
            <EmptyPage />
          </View>
        )}
      </Pressable>
      <WScreenFooter style={{ width: '100%', paddingHorizontal: 20 }}>
        <TouchableOpacity
          style={styles.buyButton}
          onPress={() => {
            showBottomSheet({
              ref: popupRef,
              enableDismiss: true,
              children: (
                <BottomSheetPromotion
                  ref={popupRef}
                  handleShow={() => handleShow()}
                  handleGetDataDiscount={value => setDataDiscount(value)}
                  handleSelectAllData={ref => hideBottomSheet(ref as any)}
                />
              ),
            });
          }}>
          <Text style={styles.actionButtonText}>Thêm sản phẩm khuyến mại</Text>
        </TouchableOpacity>
      </WScreenFooter>
      <View style={{ zIndex: 1000, position: 'absolute', top: 0, left: 0 }}>
        <ModalPromotion
          isShow={isShow}
          closeModal={closeModal}
          svgSrc={iconSvg.updateAll}
          title="Cập nhật khuyến mãi hàng loạt"
          dataDiscount={dataDiscount}
          ref={popupRef}
          handleSelectAllData={handleSelectAllData}
        />

      </View>
    </View>
  );
};

const BottomSheetPromotion = forwardRef<
  typeof FBottomSheet,
  {
    handleShow: (data?: any) => void;
    handleGetDataDiscount: (data: any[]) => void;
    handleSelectAllData: (ref: any) => void;
  }
>((props, ref) => {
  const [currentPage, setCurrentPage] = useState<number>(0);
  const [parentCategories, setParentCategories] = useState<any[]>([]);
  const [childCategories, setChildCategories] = useState<any[]>([]);
  const [products, setProducts] = useState<any[]>([]);
  const [selectedCategory, setSelectedCategory] = useState<any>(null);
  const [selectedChildCategory, setSelectedChildCategory] = useState<any>(null);
  const [selectedProducts, setSelectedProducts] = useState<any[]>([]);
  const [searchText, setSearchText] = useState<string>('');
  const [filteredData, setFilteredData] = useState<any[]>([]);

  const categoryDA = new DataController('Category');
  const productDA = new DataController('Product');
  const shopInfo = useSelectorShopState().data;

  // Load parent categories when component mounts
  useEffect(() => {
    console.log('BottomSheetPromotion: useEffect triggered - TESTING loadParentCategories');
    try {
      console.log('About to call loadParentCategories...');
      loadParentCategories();
      console.log('loadParentCategories called successfully');
    } catch (error: any) {
      console.error('Error in BottomSheetPromotion useEffect:', error);
      console.error('Error message:', error?.message);
    }
  }, [shopInfo]);

  // Filter data based on search text and current page
  useEffect(() => {
    if (currentPage === 0) {
      // Filter parent categories
      if (searchText.trim()) {
        const filtered = parentCategories.filter(item =>
          item.Name.toLowerCase().includes(searchText.toLowerCase())
        );
        setFilteredData(filtered);
      } else {
        setFilteredData(parentCategories);
      }
    } else if (currentPage === 1) {
      // Filter child categories
      if (searchText.trim()) {
        const filtered = childCategories.filter(item =>
          item.Name.toLowerCase().includes(searchText.toLowerCase())
        );
        setFilteredData(filtered);
      } else {
        setFilteredData(childCategories);
      }
    } else if (currentPage === 2) {
      // Filter products
      if (searchText.trim()) {
        const filtered = products.filter(item =>
          item.Name.toLowerCase().includes(searchText.toLowerCase())
        );
        setFilteredData(filtered);
      } else {
        setFilteredData(products);
      }
    }
  }, [searchText, parentCategories, childCategories, products, currentPage]);



  // Load parent categories (ParentId is empty)
  const loadParentCategories = async () => {
    try {
      console.log('=== loadParentCategories START ===');
      console.log('About to call categoryDA.filterByEmptyKey...');

      const response = await categoryDA.filterByEmptyKey({
        key: 'ParentId',
        notEmpty: false,
        sortby: [{ prop: 'Name', direction: 'ASC' }],
      });

      console.log('=== loadParentCategories RESPONSE ===');
      console.log('Parent categories response:', response);

      if (response?.code === 200 && response?.data) {
        console.log('Parent categories data:', response.data);
        // Log first few items to check ID format
        if (response.data.length > 0) {
          console.log('First category:', response.data[0]);
          console.log('First category ID:', response.data[0].Id, 'Type:', typeof response.data[0].Id);
        }
        setParentCategories(response.data);
        console.log('=== loadParentCategories SUCCESS ===');
      } else {
        console.log('=== loadParentCategories NO DATA ===');
      }
    } catch (error: any) {
      console.error('=== loadParentCategories ERROR ===');
      console.error('Error loading parent categories:', error);
      console.error('Error message:', error?.message);
      console.error('Error stack:', error?.stack);
    }
  };





  // Load products based on selected category
  const loadProducts = async (categoryId: string) => {
    try {
      if (!categoryId) {
        console.error('Missing categoryId');
        return;
      }

      if (!shopInfo || !shopInfo[0] || !shopInfo[0].Id) {
        console.error('Missing shopInfo or shopId');
        return;
      }

      console.log('Loading products for categoryId:', categoryId, 'shopId:', shopInfo[0].Id);

      // Use getListSimple with simpler query format
      const shopId = shopInfo[0].Id;
      const query = `@ShopId:${shopId} @CategoryId:${categoryId}`;
      console.log('Query string:', query);

      const response = await productDA.getListSimple({
        query: query,
        sortby: { BY: 'Name', DIRECTION: 'ASC' },
      });

      console.log('Products response:', response);
      if (response?.code === 200 && response?.data) {
        // Filter out products that already have discount
        const productsWithoutDiscount = response.data.filter((item: any) => !item.Discount || item.Discount === 0);
        console.log('Products without discount:', productsWithoutDiscount);
        setProducts(productsWithoutDiscount);
      }
    } catch (error) {
      console.error('Error loading products:', error);
    }
  };

  // Handle category selection on page 0
  const handleParentCategorySelect = (category: any) => {
    setSelectedCategory(category);
  };

  // Handle child category selection on page 1
  const handleChildCategorySelect = (category: any) => {
    setSelectedChildCategory(category);
  };

  // Handle product selection on page 2
  const handleProductSelect = (product: any) => {
    const isSelected = selectedProducts.find(p => p.Id === product.Id);
    if (isSelected) {
      setSelectedProducts(selectedProducts.filter(p => p.Id !== product.Id));
    } else {
      setSelectedProducts([...selectedProducts, product]);
    }
  };

  // Handle arrow click to navigate to next page
  const handleArrowClick = async (item: any) => {
    if (currentPage === 0) {
      // Navigate to child categories or directly to products if no children
      setSelectedCategory(item);

      // Check for child categories
      try {
        if (!item?.Id) {
          console.error('Missing item ID');
          return;
        }

        const parentId = item.Id;
        console.log('Checking child categories for parentId:', parentId);

        // Use getListSimple with a simpler query format
        const response = await categoryDA.getListSimple({
          query: `@ParentId:${parentId}`,
          sortby: { BY: 'Name', DIRECTION: 'ASC' },
        });

        if (response?.code === 200 && response?.data && response.data.length > 0) {
          // Has child categories, go to child categories page
          setChildCategories(response.data);
          setCurrentPage(1);
        } else {
          // No child categories, go directly to products
          await loadProducts(item.Id);
          setCurrentPage(2);
        }
        setSearchText('');
      } catch (error) {
        console.error('Error checking child categories:', error);
        // Fallback to products page
        await loadProducts(item.Id);
        setCurrentPage(2);
        setSearchText('');
      }
    } else if (currentPage === 1) {
      // Navigate to products
      setSelectedChildCategory(item);
      await loadProducts(item.Id);
      setCurrentPage(2);
      setSearchText('');
    }
  };

  // Handle back button
  const handleBack = () => {
    if (currentPage === 1) {
      setCurrentPage(0);
      setSelectedCategory(null);
      setChildCategories([]);
      setSearchText('');
    } else if (currentPage === 2) {
      setCurrentPage(1);
      setSelectedChildCategory(null);
      setProducts([]);
      setSearchText('');
    }
  };

  // Handle confirm button
  const handleConfirm = async () => {
    if (currentPage === 0 && selectedCategory) {
      // Check if category has children first
      try {
        if (!selectedCategory?.Id) {
          console.error('Missing selected category ID');
          return;
        }

        const parentId = selectedCategory.Id;
        console.log('Confirm checking child categories for parentId:', parentId);

        // Use getListSimple with a simpler query format
        const response = await categoryDA.getListSimple({
          query: `@ParentId:${parentId}`,
          sortby: { BY: 'Name', DIRECTION: 'ASC' },
        });

        if (response?.code === 200 && response?.data && response.data.length > 0) {
          // Has child categories, go to child categories page
          setChildCategories(response.data);
          setCurrentPage(1);
        } else {
          // No child categories, go directly to products
          await loadProducts(selectedCategory.Id);
          setCurrentPage(2);
        }
        setSearchText('');
      } catch (error) {
        console.error('Error checking child categories:', error);
        // Fallback to products page
        await loadProducts(selectedCategory.Id);
        setCurrentPage(2);
        setSearchText('');
      }
    } else if (currentPage === 1 && selectedChildCategory) {
      // Navigate to products
      await loadProducts(selectedChildCategory.Id);
      setCurrentPage(2);
      setSearchText('');
    } else if (currentPage === 2 && selectedProducts.length > 0) {
      // Show modal to input discount
      props.handleGetDataDiscount(selectedProducts);
      props.handleShow();
    }
  };

  // Check if confirm button should be disabled
  const isConfirmDisabled = () => {
    if (currentPage === 0) return !selectedCategory;
    if (currentPage === 1) return !selectedChildCategory;
    if (currentPage === 2) return selectedProducts.length === 0;
    return true;
  };

  // Get page title
  const getPageTitle = () => {
    if (currentPage === 0) return 'Chọn danh mục';
    if (currentPage === 1) return `Danh mục con - ${selectedCategory?.Name}`;
    if (currentPage === 2) return `Sản phẩm - ${selectedChildCategory?.Name}`;
    return 'Chọn danh mục';
  };

  // Render item for each page
  const renderItem = (item: any, index: number) => {
    if (currentPage === 0) {
      // Parent categories
      const isSelected = selectedCategory?.Id === item.Id;
      return (
        <TouchableOpacity
          key={`parent-${index}`}
          style={styles.categoryItem}
          onPress={() => handleParentCategorySelect(item)}
        >
          <View style={styles.categoryItemContent}>
            {/* Checkbox */}
            <View style={[
              styles.checkbox,
              isSelected && styles.checkboxSelected
            ]}>
              {isSelected && (
                <Text style={styles.checkmark}>✓</Text>
              )}
            </View>

            {/* Category name */}
            <Text style={[
              styles.categoryItemText,
              isSelected && styles.categoryItemTextSelected
            ]}>
              {item.Name}
            </Text>

            {/* Arrow */}
            <TouchableOpacity
              style={styles.categoryArrowButton}
              onPress={() => handleArrowClick(item)}
            >
              <Text style={styles.categoryArrowText}>›</Text>
            </TouchableOpacity>
          </View>
        </TouchableOpacity>
      );
    } else if (currentPage === 1) {
      // Child categories
      return (
        <TouchableOpacity
          key={`child-${index}`}
          style={[
            styles.itemContainer,
            selectedChildCategory?.Id === item.Id && styles.selectedItemContainer
          ]}
          onPress={() => handleChildCategorySelect(item)}
        >
          <View style={styles.itemContent}>
            <Text style={styles.itemText}>{item.Name}</Text>
            <TouchableOpacity
              style={styles.arrowButton}
              onPress={() => handleArrowClick(item)}
            >
              <Text style={styles.arrowText}>→</Text>
            </TouchableOpacity>
          </View>
        </TouchableOpacity>
      );
    } else if (currentPage === 2) {
      // Products
      const isSelected = selectedProducts.find(p => p.Id === item.Id);
      return (
        <TouchableOpacity
          key={`product-${index}`}
          style={[
            styles.itemContainer,
            isSelected && styles.selectedItemContainer
          ]}
          onPress={() => handleProductSelect(item)}
        >
          <View style={styles.itemContent}>
            <Image
              source={{ uri: ConfigAPI.urlImg + item.Img }}
              style={styles.productImage}
            />
            <Text style={styles.itemText}>{item.Name}</Text>
            <Text style={styles.priceText}>{item.Price} VNĐ</Text>
          </View>
        </TouchableOpacity>
      );
    }
    return null;
  };





  try {
    return (
      <Pressable
        style={{
          width: '100%',
          height: Dimensions.get('window').height - 100,
          borderTopLeftRadius: 16,
          borderTopRightRadius: 16,
          backgroundColor: ColorThemes.light.neutral_absolute_background_color,
          position: 'relative',
        }}>
        {/* Custom Header */}
        <View style={styles.customHeader}>
          <View style={styles.headerContent}>
            {/* Left side - Close or Back button */}
            <TouchableOpacity
              style={styles.headerButton}
              onPress={currentPage > 0 ? handleBack : () => props.handleSelectAllData(ref)}
            >
              {currentPage > 0 ? (
                <AppSvg SvgSrc={iconSvg.arrowLeft} size={20} />
              ) : (
                <AppSvg SvgSrc={iconSvg.close} size={20} />
              )}
            </TouchableOpacity>

            {/* Center - Title */}
            <Text style={styles.headerTitle}>{getPageTitle()}</Text>

            {/* Right side - "Đã chọn" button for category selection */}
            {currentPage === 0 && (
              <TouchableOpacity
                style={[
                  styles.selectedButton,
                  !selectedCategory && styles.selectedButtonDisabled
                ]}
                onPress={handleConfirm}
                disabled={!selectedCategory}
              >
                <Text style={[
                  styles.selectedButtonText,
                  !selectedCategory && styles.selectedButtonTextDisabled
                ]}>
                  Đã chọn
                </Text>
              </TouchableOpacity>
            )}
            {currentPage !== 0 && <View style={{ width: 60 }} />}
          </View>
        </View>
      <KeyboardAvoidingView
        style={{ flex: 1, position: 'relative' }}
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        keyboardVerticalOffset={Platform.OS === 'ios' ? 65 : 0}
      >
        {/* Search Bar - Only show on first page */}
        {currentPage === 0 && (
          <View style={styles.searchContainer}>
            <View style={styles.searchInputContainer}>
              <AppSvg SvgSrc={iconSvg.search} size={16} />
              <TextInput
                style={styles.searchInput}
                placeholder="Search"
                value={searchText}
                onChangeText={setSearchText}
                placeholderTextColor="#999"
              />
              <TouchableOpacity style={styles.dropdownButton}>
                <Text style={styles.dropdownText}>▼</Text>
              </TouchableOpacity>
            </View>
          </View>
        )}

        {/* Category selection text - Only show on first page */}
        {currentPage === 0 && (
          <View style={styles.categorySelectionContainer}>
            <Text style={styles.categorySelectionText}>Chọn tất cả danh mục</Text>
          </View>
        )}

        {/* Content */}
        <View style={{ flex: 1, padding: 16 }}>
          <ScrollView style={{ flex: 1 }}>
            {filteredData && filteredData.length > 0 ? (
              filteredData.map((item, index) => renderItem(item, index))
            ) : (
              <View style={{ flex: 1, alignItems: 'center', justifyContent: 'center', paddingTop: 100 }}>
                {currentPage === 2 ? (
                  <EmptyPage title="Không có dữ liệu sản phẩm" />
                ) : (
                  <EmptyPage />
                )}
              </View>
            )}
          </ScrollView>
        </View>

        {/* Action Buttons */}
        <View style={styles.actionButtonsContainer}>
          <View style={styles.actionButtonsRow}>
            <TouchableOpacity
              style={styles.bottomCancelButton}
              onPress={() => props.handleSelectAllData(ref)}
            >
              <Text style={styles.bottomCancelButtonText}>Đóng</Text>
            </TouchableOpacity>
            <TouchableOpacity
              style={[
                styles.bottomConfirmButton,
                isConfirmDisabled() && styles.bottomConfirmButtonDisabled
              ]}
              onPress={handleConfirm}
              disabled={isConfirmDisabled()}
            >
              <Text style={[
                styles.bottomConfirmButtonText,
                isConfirmDisabled() && styles.bottomConfirmButtonTextDisabled
              ]}>
                {currentPage === 2 ? 'Xác nhận' : 'Tiếp theo'}
              </Text>
            </TouchableOpacity>
          </View>
        </View>
      </KeyboardAvoidingView>
    </Pressable>
  );
  } catch (error: any) {
    console.error('=== BottomSheetPromotion RENDER ERROR ===');
    console.error('Error rendering BottomSheetPromotion:', error);
    console.error('Error message:', error?.message);
    console.error('Error stack:', error?.stack);

    // Return a simple error view
    return (
      <View style={{
        width: '100%',
        height: 200,
        backgroundColor: '#fff',
        justifyContent: 'center',
        alignItems: 'center',
        padding: 20,
      }}>
        <Text style={{ color: 'red', textAlign: 'center' }}>
          Error loading bottom sheet: {error?.message || 'Unknown error'}
        </Text>
        <TouchableOpacity
          style={{ marginTop: 10, padding: 10, backgroundColor: '#f0f0f0' }}
          onPress={() => props.handleSelectAllData(ref)}
        >
          <Text>Close</Text>
        </TouchableOpacity>
      </View>
    );
  }
});

const styles = StyleSheet.create({
  content: {
    flex: 1,
    alignItems: 'center',
  },
  PromotionMenu: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingLeft: 16,
    paddingRight: 16,
    paddingTop: 20,
    paddingBottom: 4,
  },
  label: {
    ...TypoSkin.title3,
    marginRight: 26,
    fontWeight: '400',
  },
  deleteButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: ColorThemes.light.error_border_color,
    paddingVertical: 5,
    paddingHorizontal: 15,
    borderRadius: 10,
    marginRight: 8,
  },
  updateButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: ColorThemes.light.success_border_color,
    paddingVertical: 5,
    paddingHorizontal: 15,
    borderRadius: 10,
  },
  buttonText: {
    color: ColorThemes.light.neutral_text_subtitle_color,
    fontSize: 12,
    marginLeft: 4,
  },
  buttonTextTwo: {
    color: ColorThemes.light.neutral_absolute_background_color,
    fontSize: 12,
    marginLeft: 4,
  },
  buttonTextSuccess: {
    color: ColorThemes.light.success_main_color,
    fontSize: 12,
    marginLeft: 4,
  },
  PromorionNabar: {
    backgroundColor: '#fff',
    marginLeft: 2,
    marginTop: 10,
    marginBottom: 10,
    maxHeight: 24,
  },
  activeButton: {
    backgroundColor: ColorThemes.light.primary_background,
    paddingVertical: 5,
    paddingHorizontal: 15,
    borderRadius: 20,
    marginLeft: 16,
  },
  button: {
    backgroundColor: '#f0f0f0',
    paddingVertical: 5,
    paddingHorizontal: 15,
    borderRadius: 20,
    marginLeft: 16,
  },
  buttonTextMenu: {
    color: ColorThemes.light.primary_sub_color,
    fontSize: 12,
    fontWeight: '400',
  },
  menuDetail: {
    flex: 1,
    padding: 10,
    width: '100%',
    backgroundColor: '#fff',
    marginLeft: 12,
    marginRight: 12,
    marginBottom: 12,
    marginTop: 12,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingBottom: 10,
    borderBottomWidth: 1,
    borderBottomColor: '#ccc',
  },
  headerText: {
    fontWeight: 'bold',
    ...TypoSkin.body2,
  },
  row: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 10,
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
  },
  imageContainer: {
    marginRight: 30,
    marginLeft: 30,
  },
  image: {
    width: 40,
    height: 40,
    borderRadius: 20,
  },
  text: {
    flex: 1,
    ...TypoSkin.body3,
    marginLeft: '3%',
  },
  textTwo: {
    flex: 1,
    ...TypoSkin.body3,
    marginLeft: '15%',
  },
  actions: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 6,
  },
  editIcon: {
    fontSize: 16,
    color: '#000',
    marginRight: 10,
  },
  checkIcon: {
    fontSize: 16,
    color: '#00cc00',
    marginRight: 10,
  },
  deleteIcon: {
    fontSize: 16,
    color: '#ff0000',
  },
  checkboxOld: {
    marginRight: 10,
  },
  buyButton: {
    backgroundColor: 'blue',
    width: '100%',
    alignItems: 'center',
    justifyContent: 'center',
    minHeight: 50,
    borderRadius: 30,
  },

  actionButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '500',
    textAlign: 'center',
  },
  cancelButton: {
    backgroundColor: '#f0f0f0', // Màu xám nhạt cho nút "Đóng"
    paddingVertical: 10,
    paddingHorizontal: 20,
    borderRadius: 5,
    borderWidth: 1,
    borderColor: '#ccc',
    width: '45%',
    alignItems: 'center',
    color: 'white',
  },
  confirmButton: {
    backgroundColor: '#007AFF', // Màu xanh cho nút "Xác nhận"
    paddingVertical: 10,
    paddingHorizontal: 20,
    borderRadius: 5,
    width: '45%',
    alignItems: 'center',
    color: 'white',
  },
  itemContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
    paddingLeft: 10,
    paddingRight: 20,
    paddingTop: 10,
    paddingBottom: 10,
    height: 50,
    backgroundColor: 'red',
  },
  itemText: {
    fontSize: 20,
    color: 'black',
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
  },
  // Modal styles
  modalOverlay: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  modalContainer: {
    backgroundColor: 'white',
    padding: 20,
    borderRadius: 10,
    width: '80%',
    maxHeight: '80%',
    alignItems: 'center',
  },
  modalText: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 10,
    textAlign: 'center',
  },
  modalSubText: {
    fontSize: 16,
    textAlign: 'center',
    color: '#666',
    marginBottom: 20,
  },
  modalButtonContainer: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    width: '100%',
    marginTop: 20,
  },
  modalButton: {
    padding: 10,
    borderRadius: 5,
    width: '40%',
    alignItems: 'center',
  },
  modalCancelButton: {
    backgroundColor: '#f0f0f0',
    borderWidth: 1,
    borderColor: '#ccc',
  },
  modalCancelButtonText: {
    color: '#333',
    fontSize: 16,
    fontWeight: 'bold',
  },
  modalDeleteConfirmButton: {
    backgroundColor: '#ff0000',
  },
  modalDeleteConfirmButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: 'bold',
  },
  // New styles for PageView
  selectedItemContainer: {
    backgroundColor: ColorThemes.light.primary_background,
    borderColor: ColorThemes.light.primary_main_color,
    borderWidth: 2,
  },
  itemContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    width: '100%',
  },
  arrowButton: {
    padding: 8,
    backgroundColor: ColorThemes.light.primary_main_color,
    borderRadius: 20,
    minWidth: 36,
    alignItems: 'center',
  },
  arrowText: {
    color: 'white',
    fontSize: 16,
    fontWeight: 'bold',
  },
  productImage: {
    width: 40,
    height: 40,
    borderRadius: 20,
    marginRight: 12,
  },
  priceText: {
    fontSize: 14,
    color: ColorThemes.light.error_main_color,
    fontWeight: '500',
  },
  // Custom header styles
  customHeader: {
    backgroundColor: '#fff',
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  headerContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  headerButton: {
    padding: 8,
    borderRadius: 20,
    minWidth: 40,
    alignItems: 'center',
    justifyContent: 'center',
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#000',
    textAlign: 'center',
    flex: 1,
  },
  selectedButton: {
    backgroundColor: '#007AFF',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    minWidth: 60,
    alignItems: 'center',
  },
  selectedButtonDisabled: {
    backgroundColor: '#f0f0f0',
  },
  selectedButtonText: {
    color: '#fff',
    fontSize: 14,
    fontWeight: '500',
  },
  selectedButtonTextDisabled: {
    color: '#999',
  },
  // Search bar styles
  searchContainer: {
    backgroundColor: '#fff',
    paddingHorizontal: 16,
    paddingVertical: 12,
  },
  searchInputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#f5f5f5',
    borderRadius: 25,
    paddingHorizontal: 16,
    paddingVertical: 8,
    marginBottom: 8,
  },
  searchInput: {
    flex: 1,
    marginLeft: 8,
    fontSize: 16,
    color: '#000',
    paddingVertical: 4,
  },
  dropdownButton: {
    padding: 4,
  },
  dropdownText: {
    fontSize: 12,
    color: '#999',
  },
  categorySelectionContainer: {
    paddingHorizontal: 16,
    paddingBottom: 8,
    backgroundColor: '#fff',
  },
  categorySelectionText: {
    fontSize: 16,
    color: '#007AFF',
    fontWeight: '500',
  },
  // Category item styles
  categoryItem: {
    backgroundColor: '#fff',
    marginBottom: 1,
    paddingHorizontal: 16,
    paddingVertical: 16,
  },
  categoryItemContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  checkbox: {
    width: 24,
    height: 24,
    borderRadius: 4,
    borderWidth: 2,
    borderColor: '#ddd',
    marginRight: 12,
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#fff',
  },
  checkboxSelected: {
    backgroundColor: '#007AFF',
    borderColor: '#007AFF',
  },
  checkmark: {
    color: '#fff',
    fontSize: 14,
    fontWeight: 'bold',
  },
  categoryItemText: {
    flex: 1,
    fontSize: 16,
    color: '#000',
    fontWeight: '400',
  },
  categoryItemTextSelected: {
    color: '#000',
    fontWeight: '500',
  },
  categoryArrowButton: {
    padding: 8,
    alignItems: 'center',
    justifyContent: 'center',
  },
  categoryArrowText: {
    fontSize: 20,
    color: '#999',
    fontWeight: '300',
  },
  // Bottom action buttons styles
  actionButtonsContainer: {
    backgroundColor: '#fff',
    paddingHorizontal: 16,
    paddingVertical: 16,
    borderTopWidth: 1,
    borderTopColor: '#f0f0f0',
  },
  actionButtonsRow: {
    flexDirection: 'row',
    gap: 12,
  },
  bottomCancelButton: {
    flex: 1,
    backgroundColor: '#f5f5f5',
    paddingVertical: 14,
    borderRadius: 8,
    alignItems: 'center',
    justifyContent: 'center',
  },
  bottomCancelButtonText: {
    fontSize: 16,
    color: '#666',
    fontWeight: '500',
  },
  bottomConfirmButton: {
    flex: 1,
    backgroundColor: '#007AFF',
    paddingVertical: 14,
    borderRadius: 8,
    alignItems: 'center',
    justifyContent: 'center',
  },
  bottomConfirmButtonDisabled: {
    backgroundColor: '#ccc',
  },
  bottomConfirmButtonText: {
    fontSize: 16,
    color: '#fff',
    fontWeight: '600',
  },
  bottomConfirmButtonTextDisabled: {
    color: '#999',
  },
});

export default ShopPromortionComponent;
